"""
LLM Model Manager - A flexible class for loading and managing different LLM models
Supports GPU acceleration with CUDA and easy model switching
"""

import torch
from transformers import <PERSON>Tokenizer, AutoModelForCausalLM, pipeline
import logging
from typing import Optional, Dict, Any
import os

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LLMModelManager:
    """
    A flexible model manager that can load different LLM models by name/path
    and automatically utilize GPU if available.
    """
    
    def __init__(self, model_name: str = "openai-community/gpt2-large", use_gpu: bool = True):
        """
        Initialize the model manager
        
        Args:
            model_name (str): HuggingFace model name or local path
            use_gpu (bool): Whether to use GPU if available
        """
        self.model_name = model_name
        self.use_gpu = use_gpu and torch.cuda.is_available()
        self.device = "cuda" if self.use_gpu else "cpu"
        
        # Model components
        self.tokenizer = None
        self.model = None
        self.pipeline = None
        
        # Model configuration
        self.max_length = 512
        self.max_new_tokens = 100
        self.temperature = 0.7
        self.top_p = 0.9
        self.do_sample = True
        
        logger.info(f"Initializing LLM Model Manager")
        logger.info(f"Model: {self.model_name}")
        logger.info(f"Device: {self.device}")
        if self.use_gpu:
            logger.info(f"GPU: {torch.cuda.get_device_name()}")
            logger.info(f"CUDA Version: {torch.version.cuda}")
    
    def load_model(self) -> bool:
        """
        Load the specified model and tokenizer
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.info(f"Loading model: {self.model_name}")
            
            # Load tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
            
            # Add padding token if it doesn't exist
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            # Load model with appropriate device mapping
            if self.use_gpu:
                self.model = AutoModelForCausalLM.from_pretrained(
                    self.model_name,
                    torch_dtype=torch.float16,  # Use half precision for GPU efficiency
                    device_map="auto",
                    trust_remote_code=True
                )
            else:
                self.model = AutoModelForCausalLM.from_pretrained(
                    self.model_name,
                    torch_dtype=torch.float32,
                    trust_remote_code=True
                )
                self.model.to(self.device)
            
            # Create text generation pipeline
            # Don't specify device when using accelerate (device_map="auto")
            if self.use_gpu:
                self.pipeline = pipeline(
                    "text-generation",
                    model=self.model,
                    tokenizer=self.tokenizer,
                    torch_dtype=torch.float16
                )
            else:
                self.pipeline = pipeline(
                    "text-generation",
                    model=self.model,
                    tokenizer=self.tokenizer,
                    device=-1,  # CPU only
                    torch_dtype=torch.float32
                )
            
            logger.info("Model loaded successfully!")
            return True
            
        except Exception as e:
            logger.error(f"Error loading model: {str(e)}")
            return False
    
    def generate_response(self, prompt: str, max_new_tokens: int = 100) -> str:
        """
        Generate a response to the given prompt

        Args:
            prompt (str): Input prompt
            max_new_tokens (int): Maximum number of new tokens to generate

        Returns:
            str: Generated response
        """
        if self.pipeline is None:
            return "Error: Model not loaded. Please load a model first."

        try:
            # Generate response with better parameters to prevent repetition
            outputs = self.pipeline(
                prompt,
                max_new_tokens=max_new_tokens,
                temperature=self.temperature,
                top_p=self.top_p,
                do_sample=self.do_sample,
                pad_token_id=self.tokenizer.eos_token_id,
                eos_token_id=self.tokenizer.eos_token_id,
                return_full_text=False,  # Only return the generated part
                clean_up_tokenization_spaces=True,
                repetition_penalty=1.2,  # Stronger penalty for repetition
                no_repeat_ngram_size=2,  # Prevent repeating 2-grams
                num_return_sequences=1   # Only return one sequence
            )

            # Extract the generated text
            response = outputs[0]['generated_text'].strip()

            # Clean up the response
            response = self._clean_response(response, prompt)

            return response

        except Exception as e:
            logger.error(f"Error generating response: {str(e)}")
            return f"Error generating response: {str(e)}"

    def _clean_response(self, response: str, original_prompt: str) -> str:
        """
        Clean up the generated response to remove artifacts and repetition

        Args:
            response (str): Raw generated response
            original_prompt (str): Original input prompt

        Returns:
            str: Cleaned response
        """
        import re

        # Remove the original prompt if it appears in the response
        if original_prompt.strip() in response:
            response = response.replace(original_prompt.strip(), "").strip()

        # Comprehensive persona/role pattern detection
        persona_patterns = [
            r'\b(Human|Assistant|User|Bot|AI|Client|Customer|Agent|Person|Student|Teacher|Professor|Manager|Employee|Doctor|Patient|Interviewer|Interviewee)[\s]*\d*[\s]*:',
            r'\b[A-Z][a-z]*[\s]*\d*[\s]*:',  # Any capitalized word followed by colon
            r'\b[A-Z]\.[\s]*:',  # Single letter patterns like "A.:", "B.:"
            r'\b[A-Z]\.[\s]+',   # Single letter patterns like "A. ", "B. "
            r'^\s*[A-Z][\s]*\.',  # Starting with letter and dot
            r'^\s*\d+[\s]*\.',    # Starting with number and dot
        ]

        # Find and remove multi-persona conversations
        for pattern in persona_patterns:
            matches = list(re.finditer(pattern, response, re.IGNORECASE | re.MULTILINE))
            if matches:
                # Take only content before the first persona indicator
                first_match = matches[0]
                response = response[:first_match.start()].strip()
                break

        # Remove specific conversation artifacts and patterns
        artifact_patterns = [
            r'Client:.*',
            r'Customer:.*',
            r'A\.\s*:.*',
            r'B\.\s*:.*',
            r'A\.\s+.*',
            r'B\.\s+.*',
            r'\([^)]*\)',  # Remove content in parentheses
            r'data-testid="[^"]*"',  # Remove test artifacts
            r'#[a-zA-Z0-9@]+#',  # Remove hash patterns
            r'HUMAN\s+\d+\.\d+.*project.*',  # Remove HUMAN project references
            r'teach\s+itself\s+programming.*',  # Remove specific artifacts
            r'@[a-zA-Z0-9]+',  # Remove @ mentions
        ]

        for pattern in artifact_patterns:
            response = re.sub(pattern, '', response, flags=re.IGNORECASE | re.DOTALL)

        # Remove prompt keywords that might leak through
        prompt_keywords = ["Human:", "Assistant:", "human:", "assistant:", "You:", "Me:", "User:", "Bot:"]
        for keyword in prompt_keywords:
            if keyword in response:
                parts = response.split(keyword)
                response = parts[0].strip()
                break

        # Clean up multiple spaces, newlines, and broken sentences
        response = re.sub(r'\s+', ' ', response).strip()
        response = re.sub(r'\b(is a|that a|for a|to a|of a|in a|on a|at a)\s*\.', '.', response)
        response = re.sub(r'\.\s*\.+', '.', response)
        response = re.sub(r'\s+\.', '.', response)

        # Remove repetitive word patterns
        words = response.split()
        if len(words) > 2:
            cleaned_words = []
            prev_word = ""
            repeat_count = 0

            for word in words:
                if word.lower() == prev_word.lower():
                    repeat_count += 1
                    if repeat_count < 2:  # Allow one repetition
                        cleaned_words.append(word)
                else:
                    repeat_count = 0
                    cleaned_words.append(word)
                    prev_word = word

                # Stop if we have enough content
                if len(cleaned_words) > 30:
                    break

            response = ' '.join(cleaned_words)

        # Split by conversation separators and take first coherent part
        separators = ['\n\n', '. Assistant', '. Human', ' Assistant:', ' Human:', '?Patient', '?Doctor', '?Client', '?Customer']
        for sep in separators:
            if sep in response:
                response = response.split(sep)[0].strip()
                break

        # Ensure proper sentence ending
        if response and not response.endswith(('.', '!', '?', ':')):
            # Find the last complete sentence
            last_punct = max(response.rfind('.'), response.rfind('!'), response.rfind('?'))
            if last_punct > len(response) * 0.6:  # If punctuation is reasonably near the end
                response = response[:last_punct + 1]
            else:
                response += '.'

        # Final cleanup
        response = response.strip()

        # Enhanced response validation and fallback
        if self._is_invalid_response(response, original_prompt):
            response = self._generate_contextual_fallback(original_prompt)

        return response

    def _is_invalid_response(self, response: str, original_prompt: str) -> bool:
        """
        Check if the response is invalid or irrelevant

        Args:
            response (str): Generated response
            original_prompt (str): Original prompt

        Returns:
            bool: True if response is invalid
        """
        # Check if response is too short or empty
        if len(response.strip()) < 5:
            return True

        # Check for remaining artifacts
        artifact_indicators = [
            'data-testid', '#', '@', 'Client:', 'Customer:', 'A.:', 'B.:',
            'Human:', 'Assistant:', 'HUMAN', 'project', 'compiler'
        ]

        for indicator in artifact_indicators:
            if indicator in response:
                return True

        # Check if response is just punctuation or nonsense
        if re.match(r'^[^a-zA-Z]*$', response):
            return True

        # Check for very repetitive responses
        words = response.split()
        if len(words) > 3:
            unique_words = set(word.lower() for word in words)
            if len(unique_words) / len(words) < 0.3:  # Less than 30% unique words
                return True

        return False

    def _generate_contextual_fallback(self, original_prompt: str) -> str:
        """
        Generate a contextual fallback response based on the prompt

        Args:
            original_prompt (str): Original user prompt

        Returns:
            str: Contextual fallback response
        """
        prompt_lower = original_prompt.lower()

        # Programming-related queries
        if any(keyword in prompt_lower for keyword in ['python', 'programming', 'code', 'coding', 'software', 'development']):
            return "Yes, I can help you with programming questions! Python is a popular programming language. What specific aspect would you like to know about?"

        # Greeting patterns
        if any(keyword in prompt_lower for keyword in ['hello', 'hi', 'hey', 'greetings']):
            return "Hello! I'm here to help you with any questions you might have. What can I assist you with today?"

        # Question patterns
        if '?' in original_prompt:
            return "That's an interesting question! I'd be happy to help you with that. Could you provide a bit more context?"

        # Default fallback
        return "I'm here to help! Could you please rephrase your question or provide more details about what you're looking for?"
    
    def change_model(self, new_model_name: str) -> bool:
        """
        Change to a different model
        
        Args:
            new_model_name (str): New model name or path
            
        Returns:
            bool: True if successful, False otherwise
        """
        logger.info(f"Changing model from {self.model_name} to {new_model_name}")
        
        # Clear current model from memory
        if self.model is not None:
            del self.model
            del self.tokenizer
            del self.pipeline
            if self.use_gpu:
                torch.cuda.empty_cache()
        
        # Update model name and reload
        self.model_name = new_model_name
        return self.load_model()
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        Get information about the current model
        
        Returns:
            dict: Model information
        """
        info = {
            "model_name": self.model_name,
            "device": self.device,
            "use_gpu": self.use_gpu,
            "model_loaded": self.model is not None,
            "max_length": self.max_length,
            "temperature": self.temperature,
            "top_p": self.top_p
        }
        
        if self.use_gpu and torch.cuda.is_available():
            info.update({
                "gpu_name": torch.cuda.get_device_name(),
                "cuda_version": torch.version.cuda,
                "gpu_memory_allocated": f"{torch.cuda.memory_allocated() / 1024**3:.2f} GB",
                "gpu_memory_reserved": f"{torch.cuda.memory_reserved() / 1024**3:.2f} GB"
            })
        
        return info
    
    def update_generation_params(self, **kwargs):
        """
        Update generation parameters
        
        Args:
            **kwargs: Generation parameters (temperature, top_p, max_length, etc.)
        """
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
                logger.info(f"Updated {key} to {value}")
            else:
                logger.warning(f"Unknown parameter: {key}")
