# 🔧 Chatbot Troubleshooting Guide

## 🚀 Quick Start Testing

### **Step 1: Test the Chatbot**
```bash
cd C:/Users/<USER>/Desktop/llm/simple_chatbot
python chatbot.py
```

### **Step 2: Try These Test Inputs**
1. **"hello"** - Should give a clean greeting without "Client:" artifacts
2. **"do you know Python programming language?"** - Should give a relevant programming response
3. **"what is machine learning?"** - Should provide an informative answer
4. **"how are you?"** - Should respond naturally

## 🔍 If Issues Persist

### **Issue 1: Still Getting Multi-persona Responses**

**Symptoms:** Responses like `"Client: i want to book..."`

**Additional Fix:**
```python
# Add this to model_manager.py in _clean_response method after line 190
# Additional safety check for missed personas
response = re.sub(r'\b[A-Z][a-z]*\s*\d*\s*:', '', response)
response = re.sub(r'\b[A-Z]\.\s*', '', response)
```

### **Issue 2: Still Getting Technical Artifacts**

**Symptoms:** Responses with `data-testid` or `#hash#` patterns

**Additional Fix:**
```python
# Add this to model_manager.py in _clean_response method
# More aggressive artifact removal
response = re.sub(r'[#@][a-zA-Z0-9_#@]*[#@]?', '', response)
response = re.sub(r'data-[a-zA-Z-]*="[^"]*"', '', response)
response = re.sub(r'<[^>]*>', '', response)
```

### **Issue 3: Irrelevant Responses to Programming Questions**

**Symptoms:** Getting "nope" or dismissive responses to Python questions

**Check:** The contextual fallback should automatically trigger. If not, verify:
```python
# In model_manager.py, _is_invalid_response method should detect:
if any(keyword in prompt_lower for keyword in ['python', 'programming', 'code']):
    if not any(keyword in response_lower for keyword in ['python', 'programming', 'code']):
        if any(phrase in response_lower for phrase in ['nope', "don't know"]):
            return True  # This should trigger fallback
```

## 🛠️ Advanced Troubleshooting

### **If Python Environment Issues:**

1. **Check Python Installation:**
   ```bash
   python --version
   ```

2. **Check Required Packages:**
   ```bash
   pip list | findstr torch
   pip list | findstr transformers
   ```

3. **Reinstall Dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

### **If Model Loading Issues:**

1. **Check GPU Memory:**
   ```python
   import torch
   print(f"CUDA available: {torch.cuda.is_available()}")
   if torch.cuda.is_available():
       print(f"GPU memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
   ```

2. **Try CPU Mode:**
   ```bash
   python chatbot.py --no-gpu
   ```

3. **Use Smaller Model:**
   ```bash
   python chatbot.py --model distilgpt2
   ```

## 🔄 Manual Verification

### **Test the Cleaning Logic Manually:**

Create a simple test file `test_manual.py`:
```python
import re

def test_cleaning():
    test_response = "hi there, what can I do for you? Client: i want to book a ticket"
    
    # Apply persona detection
    pattern = r'\b(Client|Customer|Manager)[\s]*:'
    match = re.search(pattern, test_response)
    if match:
        cleaned = test_response[:match.start()].strip()
        print(f"Original: {test_response}")
        print(f"Cleaned: {cleaned}")
    
test_cleaning()
```

### **Expected Output:**
```
Original: hi there, what can I do for you? Client: i want to book a ticket
Cleaned: hi there, what can I do for you?
```

## 📋 Configuration Verification

### **Check chatbot_config.json:**
```json
{
  "generation_params": {
    "max_new_tokens": 80,
    "temperature": 0.3,
    "top_p": 0.8,
    "repetition_penalty": 1.2,
    "no_repeat_ngram_size": 3
  }
}
```

### **Verify Model Parameters in model_manager.py:**
```python
# In generate_response method, should have:
temperature=0.3,  # Lower for more focused responses
top_p=0.8,        # Reduced for better quality
repetition_penalty=1.3,  # Stronger penalty
no_repeat_ngram_size=3,  # Prevent 3-gram repetition
```

## 🎯 Success Indicators

### **✅ Working Correctly When:**
1. **Greetings** get clean responses without "Client:" artifacts
2. **Programming questions** get relevant programming responses
3. **No technical artifacts** like `data-testid` or `#hash#` patterns
4. **Single voice responses** without A./B./C. patterns
5. **Contextual understanding** - appropriate responses for different question types

### **❌ Still Needs Work If:**
1. Multi-persona conversations still appear
2. Technical artifacts remain in responses
3. Programming questions get irrelevant responses
4. Responses are too long or repetitive

## 🚨 Emergency Fallback

### **If All Else Fails:**

1. **Reset to Basic Configuration:**
   ```json
   {
     "generation_params": {
       "max_new_tokens": 50,
       "temperature": 0.1,
       "top_p": 0.7,
       "do_sample": true
     }
   }
   ```

2. **Use Simplest Model:**
   ```bash
   python chatbot.py --model distilgpt2 --no-gpu
   ```

3. **Check for Syntax Errors:**
   ```bash
   python -m py_compile chatbot.py
   python -m py_compile model_manager.py
   python -m py_compile config.py
   ```

## 📞 Support

If issues persist after trying these solutions:

1. **Check the log files** for error messages
2. **Verify all file modifications** were applied correctly
3. **Test with minimal configuration** first
4. **Ensure Python environment** is properly set up

**The improvements should resolve all reported issues. The chatbot should now provide clean, relevant, and helpful responses!**
