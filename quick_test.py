#!/usr/bin/env python3
"""
Quick test of response cleaning improvements
"""

import sys
import os

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from model_manager import LLMModelManager

def test_response_cleaning():
    """Test the response cleaning functionality directly"""
    
    print("🧪 Testing Response Cleaning Improvements")
    print("=" * 50)
    
    # Create a model manager instance (without loading the actual model)
    model_manager = LLMModelManager("meta-llama/Llama-3.2-1B")
    
    # Test cases with problematic responses
    test_cases = [
        {
            "raw_response": "hi there, what can I do for you? Client: i want to book a ticket from london to amsterdam. A. is it possible? (no) B.",
            "prompt": "hello",
            "description": "Multi-persona conversation with Client/A/B pattern"
        },
        {
            "raw_response": "nope! What's that then? Client: well python is the best! A. : are we talking about computer science or just general knowledge? B.: yes of course!",
            "prompt": "do you know Python programming language?",
            "description": "Irrelevant response with multi-persona artifacts"
        },
        {
            "raw_response": "Hello! How can I help you today? data-testid=\"read-dictionary\" #docomputer#adocompiler#",
            "prompt": "hello",
            "description": "Response with technical artifacts"
        },
        {
            "raw_response": "HUMAN 1.0 project teach itself programming A.: what do you think? B.: sounds good!",
            "prompt": "what is programming?",
            "description": "HUMAN project reference with multi-persona"
        },
        {
            "raw_response": "Human: hello Assistant: hi there Human: how are you Assistant: I'm doing well",
            "prompt": "hello",
            "description": "Leaked conversation format"
        }
    ]
    
    print("Testing response cleaning on problematic inputs...\n")
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"🔍 Test {i}: {test_case['description']}")
        print(f"Raw response: '{test_case['raw_response']}'")
        
        # Clean the response
        cleaned_response = model_manager._clean_response(
            test_case['raw_response'], 
            test_case['prompt']
        )
        
        print(f"Cleaned response: '{cleaned_response}'")
        
        # Check for artifacts
        artifacts_found = []
        artifact_patterns = [
            'Client:', 'Customer:', 'A.:', 'B.:', 'A. ', 'B. ',
            'Human:', 'Assistant:', 'data-testid', '#', '@', 
            'HUMAN', 'project', 'compiler'
        ]
        
        for pattern in artifact_patterns:
            if pattern in cleaned_response:
                artifacts_found.append(pattern)
        
        # Evaluate result
        if artifacts_found:
            print(f"❌ Artifacts still present: {artifacts_found}")
            results.append(False)
        elif len(cleaned_response.strip()) < 5:
            print("❌ Response too short after cleaning")
            results.append(False)
        else:
            print("✅ Response cleaned successfully")
            results.append(True)
        
        print("-" * 40)
    
    # Test contextual fallbacks
    print("\n🔄 Testing Contextual Fallbacks")
    print("=" * 40)
    
    fallback_tests = [
        {
            "prompt": "do you know Python programming language?",
            "description": "Programming question fallback"
        },
        {
            "prompt": "hello there",
            "description": "Greeting fallback"
        },
        {
            "prompt": "what is machine learning?",
            "description": "Question fallback"
        }
    ]
    
    for test in fallback_tests:
        print(f"Prompt: '{test['prompt']}'")
        fallback = model_manager._generate_contextual_fallback(test['prompt'])
        print(f"Fallback: '{fallback}'")
        print()
    
    # Summary
    print("📊 Test Results")
    print("=" * 30)
    passed = sum(results)
    total = len(results)
    print(f"Cleaning tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All cleaning tests passed! Improvements are working.")
        return True
    elif passed > total * 0.7:
        print("✅ Most tests passed. Significant improvement achieved.")
        return True
    else:
        print("⚠️  Some issues remain. Further improvements needed.")
        return False

if __name__ == "__main__":
    success = test_response_cleaning()
    print(f"\n{'✅ SUCCESS' if success else '❌ NEEDS WORK'}: Response cleaning improvements")
