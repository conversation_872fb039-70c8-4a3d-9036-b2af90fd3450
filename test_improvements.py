#!/usr/bin/env python3
"""
Test script to verify chatbot improvements
"""

import sys
import os
from chatbot import CLIC<PERSON><PERSON>

def test_chatbot_responses():
    """Test the chatbot with problematic inputs"""
    
    print("🧪 Testing Chatbot Improvements")
    print("=" * 50)
    
    # Initialize chatbot
    try:
        chatbot = CLIChatbot("chatbot_config.json")
        if not chatbot.load_model():
            print("❌ Failed to load model")
            return False
        
        print("✅ Model loaded successfully")
        
    except Exception as e:
        print(f"❌ Error initializing chatbot: {e}")
        return False
    
    # Test cases that previously caused issues
    test_cases = [
        {
            "input": "hello",
            "description": "Simple greeting",
            "expected_patterns": ["hello", "help", "assist"]
        },
        {
            "input": "do you know Python programming language?",
            "description": "Python programming question",
            "expected_patterns": ["python", "programming", "language", "yes"]
        },
        {
            "input": "what is machine learning?",
            "description": "Technical question",
            "expected_patterns": ["machine", "learning", "algorithm", "data"]
        },
        {
            "input": "how are you?",
            "description": "Casual question",
            "expected_patterns": ["good", "fine", "well", "help"]
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🔍 Test {i}: {test_case['description']}")
        print(f"Input: '{test_case['input']}'")
        
        try:
            # Generate response
            response = chatbot._generate_response(test_case['input'])
            print(f"Response: '{response}'")
            
            # Check for artifacts
            artifacts_found = []
            artifact_patterns = [
                'Client:', 'Customer:', 'A.:', 'B.:', 'Human:', 'Assistant:',
                'data-testid', '#', '@', 'HUMAN', 'project', 'compiler'
            ]
            
            for pattern in artifact_patterns:
                if pattern in response:
                    artifacts_found.append(pattern)
            
            # Check response quality
            is_relevant = any(pattern.lower() in response.lower() 
                            for pattern in test_case['expected_patterns'])
            
            # Evaluate result
            if artifacts_found:
                print(f"❌ Artifacts found: {artifacts_found}")
                results.append(False)
            elif len(response.strip()) < 5:
                print("❌ Response too short")
                results.append(False)
            elif not is_relevant:
                print("⚠️  Response may not be relevant")
                results.append(True)  # Still pass if no artifacts
            else:
                print("✅ Response looks good")
                results.append(True)
                
        except Exception as e:
            print(f"❌ Error generating response: {e}")
            results.append(False)
    
    # Summary
    print(f"\n📊 Test Results")
    print("=" * 30)
    passed = sum(results)
    total = len(results)
    print(f"Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! Improvements are working.")
    elif passed > total * 0.7:
        print("✅ Most tests passed. Significant improvement achieved.")
    else:
        print("⚠️  Some issues remain. Further improvements needed.")
    
    return passed >= total * 0.7

if __name__ == "__main__":
    success = test_chatbot_responses()
    sys.exit(0 if success else 1)
