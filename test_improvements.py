#!/usr/bin/env python3
"""
Test script to verify the chatbot improvements
"""

import sys
import os
from model_manager import LLMModelManager
from config import ChatbotConfig

def test_model_parameters():
    """Test model-specific parameter configuration"""
    print("🧪 Testing Model Parameter Configuration")
    print("=" * 50)
    
    config = ChatbotConfig("chatbot_config.json")
    
    # Test different models
    test_models = [
        "openai-community/gpt2-large",
        "distilgpt2", 
        "meta-llama/Llama-3.2-1B",
        "mistralai/Mistral-7B-Instruct-v0.1"
    ]
    
    for model in test_models:
        print(f"\n📋 Model: {model}")
        params = config.get_optimized_params_for_model(model)
        print(f"  Max tokens: {params.get('max_new_tokens')}")
        print(f"  Temperature: {params.get('temperature')}")
        print(f"  Top-p: {params.get('top_p')}")
        print(f"  Top-k: {params.get('top_k')}")
        print(f"  Repetition penalty: {params.get('repetition_penalty')}")

def test_response_cleaning():
    """Test response cleaning functionality"""
    print("\n🧹 Testing Response Cleaning")
    print("=" * 50)
    
    # Initialize model manager
    model_manager = LLMModelManager("openai-community/gpt2-large", use_gpu=False)
    
    # Test cases with problematic responses
    test_cases = [
        {
            "response": "Hello! How can I help you today? Assistant: I can help with many things. Human: What about coding?",
            "prompt": "Hello",
            "expected_clean": True
        },
        {
            "response": "#docomputeregistrar#adocompiler#acompileradocompiler@authorcompilera. Hello there!",
            "prompt": "Hi",
            "expected_clean": True
        },
        {
            "response": 'data-testid="read-dictionary" Hello! How are you?',
            "prompt": "Hello",
            "expected_clean": True
        },
        {
            "response": "HUMAN 1.0/2.0 project teach itself programming Hello!",
            "prompt": "Hi",
            "expected_clean": True
        },
        {
            "response": "Student: I think this is good. Professor: Yes, I agree. Manager: Let's proceed.",
            "prompt": "What do you think?",
            "expected_clean": True
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🔍 Test Case {i}:")
        print(f"  Original: {test_case['response'][:60]}...")
        cleaned = model_manager._clean_response(test_case['response'], test_case['prompt'])
        print(f"  Cleaned:  {cleaned}")
        print(f"  Status:   {'✅ PASS' if len(cleaned) > 0 and len(cleaned) < len(test_case['response']) else '❌ FAIL'}")

def test_prompt_formatting():
    """Test prompt formatting for different models"""
    print("\n📝 Testing Prompt Formatting")
    print("=" * 50)
    
    from chatbot import CLIChatbot
    
    # Create chatbot instance
    chatbot = CLIChatbot("chatbot_config.json")
    
    # Test different models
    test_models = ["openai-community/gpt2-large", "meta-llama/Llama-3.2-1B", "mistralai/Mistral-7B-Instruct-v0.1"]
    user_input = "What is Python?"
    
    for model in test_models:
        print(f"\n🤖 Model: {model}")
        chatbot.config.set_current_model(model)
        prompt = chatbot._prepare_prompt(user_input)
        print(f"  Prompt: {prompt}")

def main():
    """Run all tests"""
    print("🚀 Chatbot Improvement Tests")
    print("=" * 60)
    
    try:
        test_model_parameters()
        test_response_cleaning()
        test_prompt_formatting()
        
        print("\n✅ All tests completed!")
        print("\n📊 Summary of Improvements:")
        print("  • Model-specific parameter optimization")
        print("  • Enhanced artifact detection and removal")
        print("  • Improved prompt formatting for different models")
        print("  • Better repetition handling")
        print("  • Contextual fallback responses")
        
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
